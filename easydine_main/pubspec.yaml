name: easydine_main
description: "EasyDine Waiter Application To Manager Orders and Reception."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev


# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  go_router: ^16.0.0
  flutter_svg: ^2.2.0
  lottie: ^3.3.1
  ribbon_widget: ^1.0.5
  google_fonts: any
  flutter_picker: any
  rive: ^0.13.20
  flutter_staggered_grid_view: ^0.7.0
  flutter_bloc: ^9.1.1
  equatable: ^2.0.7
  simple_animated_button: ^0.0.9
  animate_do: ^4.2.0
  flutter_local_notifications: ^19.4.0
  flutter_local_notifications_linux: ^6.0.0
  glassmorphism_ui: ^0.3.0
  shared_preferences: ^2.5.3
  uuid: ^4.5.1
  intl: ^0.20.2
  pdf: ^3.11.3
  path_provider: ^2.1.5
  flutter_pdfview: ^1.4.1+1
  printing: ^5.14.2
  http: ^1.4.0
  http_interceptor: ^2.0.0
  flutter_dotenv: ^5.2.1
  sizer: ^3.0.4
  # Modern printer packages with better null safety and fewer conflicts
  thermal_printer_plus: ^1.0.11
  print_bluetooth_thermal: ^1.1.6
  esc_pos_utils_plus: ^2.0.4
  flutter_bluetooth_printer: ^2.19.0
  # Keep network discovery packages
  network_info_plus: ^6.1.4
  ping_discover_network_forked: ^0.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  bloc_test: ^10.0.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  assets:
    - assets/
    - assets/fonts/

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
